<script setup>
import { ref, onMounted, nextTick, onUnmounted } from 'vue'
import { renderAsync } from 'docx-preview'

// 文档URL配置
const leftUrl = "/api/analyse_service/static/1de564aa3587153f9faee74c8cdef3f8/attachment/ИНФО_о__числен_каз_за_рубежом.docx"
const rightUrl = "/api/analyse_service/static/1de564aa3587153f9faee74c8cdef3f8/attachment/translated_ИНФО_о__числен_каз_за_рубежом.docx"

// 响应式状态
const leftContainer = ref(null)
const rightContainer = ref(null)
const leftScrollContainer = ref(null)
const rightScrollContainer = ref(null)
const leftLoading = ref(false)
const rightLoading = ref(false)
const leftError = ref('')
const rightError = ref('')

// 同步滚动状态
const isScrolling = ref(false)
const scrollTimeout = ref(null)

/**
 * 加载并渲染docx文档
 * @param {string} url - 文档URL
 * @param {HTMLElement} container - 容器元素
 * @param {Ref} loading - 加载状态
 * @param {Ref} error - 错误状态
 */
const loadDocument = async (url, container, loading, error) => {
  if (!container || !url) return

  loading.value = true
  error.value = ''

  try {
    // 清空容器内容
    container.innerHTML = ''

    // 获取文档数据
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()

    // 使用docx-preview渲染文档，优化多页显示配置
    await renderAsync(arrayBuffer, container, null, {
      className: 'docx-wrapper',
      inWrapper: true,
      ignoreWidth: false,
      ignoreHeight: false,
      ignoreFonts: false,
      breakPages: true,  // 启用分页
      ignoreLastRenderedPageBreak: false,  // 不忽略最后的分页符
      experimental: false,
      trimXmlDeclaration: true,
      useBase64URL: false,
      useMathMLPolyfill: false,
      showChanges: false,
      debug: false,
      renderHeaders: true,  // 渲染页眉
      renderFooters: true,  // 渲染页脚
      renderFootnotes: true,  // 渲染脚注
      renderEndnotes: true   // 渲染尾注
    })

  } catch (err) {
    console.error('文档加载失败:', err)
    error.value = `文档加载失败: ${err.message}`
    container.innerHTML = `
      <div class="error-message">
        <div class="error-icon">⚠️</div>
        <div class="error-text">${error.value}</div>
        <button class="retry-btn" onclick="location.reload()">重试</button>
      </div>
    `
  } finally {
    loading.value = false
  }
}

/**
 * 同步滚动处理函数
 * @param {Event} event - 滚动事件
 * @param {HTMLElement} sourceContainer - 源滚动容器
 * @param {HTMLElement} targetContainer - 目标滚动容器
 */
const handleSyncScroll = (event, sourceContainer, targetContainer) => {
  if (isScrolling.value || !sourceContainer || !targetContainer) return

  // 防止递归调用
  isScrolling.value = true

  // 计算滚动比例
  const sourceScrollTop = sourceContainer.scrollTop
  const sourceScrollHeight = sourceContainer.scrollHeight - sourceContainer.clientHeight
  const scrollRatio = sourceScrollHeight > 0 ? sourceScrollTop / sourceScrollHeight : 0

  // 应用到目标容器
  const targetScrollHeight = targetContainer.scrollHeight - targetContainer.clientHeight
  const targetScrollTop = scrollRatio * targetScrollHeight

  targetContainer.scrollTop = targetScrollTop

  // 清除之前的定时器
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }

  // 设置新的定时器来重置滚动状态
  scrollTimeout.value = setTimeout(() => {
    isScrolling.value = false
  }, 100)
}

/**
 * 设置同步滚动监听器
 */
const setupSyncScroll = () => {
  if (!leftScrollContainer.value || !rightScrollContainer.value) return

  const leftHandler = (event) => {
    handleSyncScroll(event, leftScrollContainer.value, rightScrollContainer.value)
  }

  const rightHandler = (event) => {
    handleSyncScroll(event, rightScrollContainer.value, leftScrollContainer.value)
  }

  leftScrollContainer.value.addEventListener('scroll', leftHandler, { passive: true })
  rightScrollContainer.value.addEventListener('scroll', rightHandler, { passive: true })

  // 返回清理函数
  return () => {
    if (leftScrollContainer.value) {
      leftScrollContainer.value.removeEventListener('scroll', leftHandler)
    }
    if (rightScrollContainer.value) {
      rightScrollContainer.value.removeEventListener('scroll', rightHandler)
    }
  }
}

// 存储清理函数
let cleanupSyncScroll = null

// 组件挂载后加载文档
onMounted(async () => {
  await nextTick()

  // 并行加载两个文档
  await Promise.all([
    loadDocument(leftUrl, leftContainer.value, leftLoading, leftError),
    loadDocument(rightUrl, rightContainer.value, rightLoading, rightError)
  ])

  // 文档加载完成后设置同步滚动
  await nextTick()
  cleanupSyncScroll = setupSyncScroll()
})

// 组件卸载时清理
onUnmounted(() => {
  if (cleanupSyncScroll) {
    cleanupSyncScroll()
  }
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }
})
</script>

<template>
  <div class="document-compare">
    <!-- 头部标题 -->
    <header class="header">
      <h1 class="title">文档对比工具</h1>
      <div class="subtitle">原文档与翻译文档对照查看</div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 左侧面板 - 原始文档 -->
      <div class="document-panel left-panel">
        <div class="panel-header">
          <h2 class="panel-title">原始文档</h2>
          <div v-if="leftLoading" class="loading-indicator">
            <div class="spinner"></div>
            <span>加载中...</span>
          </div>
        </div>
        <div
          ref="leftScrollContainer"
          class="document-container scroll-container"
        >
          <div
            ref="leftContainer"
            class="docx-container"
            :class="{ 'loading': leftLoading }"
          ></div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="divider">
        <div class="sync-indicator">
          <div class="sync-icon">⇄</div>
          <div class="sync-text">同步滚动</div>
        </div>
      </div>

      <!-- 右侧面板 - 翻译文档 -->
      <div class="document-panel right-panel">
        <div class="panel-header">
          <h2 class="panel-title">翻译文档</h2>
          <div v-if="rightLoading" class="loading-indicator">
            <div class="spinner"></div>
            <span>加载中...</span>
          </div>
        </div>
        <div
          ref="rightScrollContainer"
          class="document-container scroll-container"
        >
          <div
            ref="rightContainer"
            class="docx-container"
            :class="{ 'loading': rightLoading }"
          ></div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
/* 全局样式重置和基础设置 */
.document-compare {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.title {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.subtitle {
  margin-top: 0.5rem;
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 400;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: 0;
  overflow: hidden;
  min-height: 0;
}

/* 文档面板样式 */
.document-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  margin: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.left-panel {
  margin-right: 0.5rem;
}

.right-panel {
  margin-left: 0.5rem;
}

/* 面板头部 */
.panel-header {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 文档容器 */
.document-container {
  flex: 1;
  overflow: auto;
  position: relative;
}

.scroll-container {
  scroll-behavior: smooth;
}

.docx-container {
  min-height: 100%;
  padding: 1rem;
  background: white;
}

.docx-container.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #718096;
}

/* 分隔线和同步指示器 */
.divider {
  width: 2px;
  background: linear-gradient(to bottom, #e2e8f0, #cbd5e0, #e2e8f0);
  margin: 1rem 0;
  flex-shrink: 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sync-indicator {
  position: absolute;
  background: white;
  border: 2px solid #4299e1;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sync-indicator:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sync-icon {
  font-size: 1.2rem;
  color: #4299e1;
  font-weight: bold;
  animation: sync-pulse 2s infinite;
}

.sync-text {
  font-size: 0.6rem;
  color: #4a5568;
  font-weight: 500;
  margin-top: 2px;
}

@keyframes sync-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 错误消息样式 */
:deep(.error-message) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #e53e3e;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin: 2rem;
  padding: 2rem;
}

:deep(.error-icon) {
  font-size: 3rem;
  margin-bottom: 1rem;
}

:deep(.error-text) {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  max-width: 400px;
  line-height: 1.5;
}

:deep(.retry-btn) {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

:deep(.retry-btn:hover) {
  background: #c53030;
}

/* docx-preview 样式优化 - 支持多页文档 */
:deep(.docx-wrapper) {
  max-width: 100%;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: visible;
  min-height: 100%;
}

/* 多页文档支持 */
:deep(.docx-wrapper > section) {
  padding: 2rem;
  line-height: 1.6;
  min-height: auto;
  page-break-after: auto;
  break-after: auto;
}

/* 分页样式 */
:deep(.docx-wrapper .page-break) {
  page-break-before: always;
  break-before: page;
  margin-top: 2rem;
  border-top: 2px dashed #e2e8f0;
  padding-top: 2rem;
}

/* 页面容器 */
:deep(.docx-wrapper .docx-page) {
  margin-bottom: 2rem;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

:deep(.docx-wrapper .docx-page:last-child) {
  margin-bottom: 0;
}

/* 文本和段落样式 */
:deep(.docx-wrapper p) {
  margin-bottom: 1rem;
  line-height: 1.6;
}

:deep(.docx-wrapper h1, .docx-wrapper h2, .docx-wrapper h3,
       .docx-wrapper h4, .docx-wrapper h5, .docx-wrapper h6) {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.4;
}

/* 表格样式 */
:deep(.docx-wrapper table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  background: white;
}

:deep(.docx-wrapper td, .docx-wrapper th) {
  border: 1px solid #e2e8f0;
  padding: 0.75rem 0.5rem;
  text-align: left;
  vertical-align: top;
}

:deep(.docx-wrapper th) {
  background-color: #f7fafc;
  font-weight: 600;
}

/* 列表样式 */
:deep(.docx-wrapper ul, .docx-wrapper ol) {
  margin: 1rem 0;
  padding-left: 2rem;
}

:deep(.docx-wrapper li) {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

/* 图片样式 */
:deep(.docx-wrapper img) {
  max-width: 100%;
  height: auto;
  margin: 1rem 0;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 页眉页脚样式 */
:deep(.docx-wrapper .docx-header, .docx-wrapper .docx-footer) {
  padding: 1rem 2rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.9rem;
  color: #4a5568;
}

:deep(.docx-wrapper .docx-footer) {
  border-top: 1px solid #e2e8f0;
  border-bottom: none;
  margin-top: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .document-panel {
    margin: 0.5rem;
  }

  .left-panel {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
  }

  .right-panel {
    margin-left: 0.5rem;
    margin-top: 0.25rem;
  }

  .divider {
    width: 100%;
    height: 2px;
    margin: 0;
    flex-direction: row;
  }

  .sync-indicator {
    width: 50px;
    height: 50px;
    position: relative;
  }

  .sync-icon {
    font-size: 1rem;
    transform: rotate(90deg);
  }

  .sync-text {
    font-size: 0.5rem;
  }

  .header {
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .panel-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .docx-container {
    padding: 0.5rem;
  }

  :deep(.docx-wrapper > section) {
    padding: 1rem;
  }
}
</style>
