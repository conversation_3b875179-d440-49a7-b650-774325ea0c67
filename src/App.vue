<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { renderAsync } from 'docx-preview'

// 文档URL配置
const leftUrl = "/api/analyse_service/static/1de564aa3587153f9faee74c8cdef3f8/attachment/ИНФО_о__числен_каз_за_рубежом.docx"
const rightUrl = "/api/analyse_service/static/1de564aa3587153f9faee74c8cdef3f8/attachment/translated_ИНФО_о__числен_каз_за_рубежом.docx"

// 响应式状态
const leftContainer = ref(null)
const rightContainer = ref(null)
const leftLoading = ref(false)
const rightLoading = ref(false)
const leftError = ref('')
const rightError = ref('')

/**
 * 加载并渲染docx文档
 * @param {string} url - 文档URL
 * @param {HTMLElement} container - 容器元素
 * @param {Ref} loading - 加载状态
 * @param {Ref} error - 错误状态
 */
const loadDocument = async (url, container, loading, error) => {
  if (!container || !url) return

  loading.value = true
  error.value = ''

  try {
    // 清空容器内容
    container.innerHTML = ''

    // 获取文档数据
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()

    // 使用docx-preview渲染文档
    await renderAsync(arrayBuffer, container, null, {
      className: 'docx-wrapper',
      inWrapper: true,
      ignoreWidth: false,
      ignoreHeight: false,
      ignoreFonts: false,
      breakPages: true,
      ignoreLastRenderedPageBreak: true,
      experimental: false,
      trimXmlDeclaration: true,
      useBase64URL: false,
      useMathMLPolyfill: false,
      showChanges: false,
      debug: false
    })

  } catch (err) {
    console.error('文档加载失败:', err)
    error.value = `文档加载失败: ${err.message}`
    container.innerHTML = `
      <div class="error-message">
        <div class="error-icon">⚠️</div>
        <div class="error-text">${error.value}</div>
        <button class="retry-btn" onclick="location.reload()">重试</button>
      </div>
    `
  } finally {
    loading.value = false
  }
}

// 组件挂载后加载文档
onMounted(async () => {
  await nextTick()

  // 并行加载两个文档
  Promise.all([
    loadDocument(leftUrl, leftContainer.value, leftLoading, leftError),
    loadDocument(rightUrl, rightContainer.value, rightLoading, rightError)
  ])
})
</script>

<template>
  <div class="document-compare">
    <!-- 头部标题 -->
    <header class="header">
      <h1 class="title">文档对比工具</h1>
      <div class="subtitle">原文档与翻译文档对照查看</div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 左侧面板 - 原始文档 -->
      <div class="document-panel left-panel">
        <div class="panel-header">
          <h2 class="panel-title">原始文档</h2>
          <div v-if="leftLoading" class="loading-indicator">
            <div class="spinner"></div>
            <span>加载中...</span>
          </div>
        </div>
        <div class="document-container">
          <div
            ref="leftContainer"
            class="docx-container"
            :class="{ 'loading': leftLoading }"
          ></div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="divider"></div>

      <!-- 右侧面板 - 翻译文档 -->
      <div class="document-panel right-panel">
        <div class="panel-header">
          <h2 class="panel-title">翻译文档</h2>
          <div v-if="rightLoading" class="loading-indicator">
            <div class="spinner"></div>
            <span>加载中...</span>
          </div>
        </div>
        <div class="document-container">
          <div
            ref="rightContainer"
            class="docx-container"
            :class="{ 'loading': rightLoading }"
          ></div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
/* 全局样式重置和基础设置 */
.document-compare {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.title {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.subtitle {
  margin-top: 0.5rem;
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 400;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  gap: 0;
  overflow: hidden;
  min-height: 0;
}

/* 文档面板样式 */
.document-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  margin: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.left-panel {
  margin-right: 0.5rem;
}

.right-panel {
  margin-left: 0.5rem;
}

/* 面板头部 */
.panel-header {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4a5568;
  font-size: 0.9rem;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 文档容器 */
.document-container {
  flex: 1;
  overflow: auto;
  position: relative;
}

.docx-container {
  min-height: 100%;
  padding: 1rem;
  background: white;
}

.docx-container.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #718096;
}

/* 分隔线 */
.divider {
  width: 2px;
  background: linear-gradient(to bottom, #e2e8f0, #cbd5e0, #e2e8f0);
  margin: 1rem 0;
  flex-shrink: 0;
}

/* 错误消息样式 */
:deep(.error-message) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  color: #e53e3e;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin: 2rem;
  padding: 2rem;
}

:deep(.error-icon) {
  font-size: 3rem;
  margin-bottom: 1rem;
}

:deep(.error-text) {
  font-size: 1rem;
  margin-bottom: 1.5rem;
  max-width: 400px;
  line-height: 1.5;
}

:deep(.retry-btn) {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

:deep(.retry-btn:hover) {
  background: #c53030;
}

/* docx-preview 样式优化 */
:deep(.docx-wrapper) {
  max-width: 100%;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

:deep(.docx-wrapper > section) {
  padding: 2rem;
  line-height: 1.6;
}

:deep(.docx-wrapper p) {
  margin-bottom: 1rem;
}

:deep(.docx-wrapper table) {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

:deep(.docx-wrapper td, .docx-wrapper th) {
  border: 1px solid #e2e8f0;
  padding: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .document-panel {
    margin: 0.5rem;
  }

  .left-panel {
    margin-right: 0.5rem;
    margin-bottom: 0.25rem;
  }

  .right-panel {
    margin-left: 0.5rem;
    margin-top: 0.25rem;
  }

  .divider {
    width: 100%;
    height: 2px;
    margin: 0;
  }

  .header {
    padding: 1rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .subtitle {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .panel-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }

  .docx-container {
    padding: 0.5rem;
  }

  :deep(.docx-wrapper > section) {
    padding: 1rem;
  }
}
</style>
