import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    port: 8080,
    proxy: {
      ['/api']: {
        target: "https://192.168.20.180",
        changeOrigin: true,
        secure: false,  // 忽略证书验证
        rewrite: (paths) => paths.replace(new RegExp('^' + '/api'), '')
      }
    },
  }
})
